import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import QtQuick.Dialogs
import Quickshell
import Quickshell.Io
import qs.Common
import qs.modules.common
import qs.modules.common.widgets

StyledPopup {
    id: root
    width: 400
    height: 200

    property url wallpaperFolder: ""      // chosen folder
    property var wallpapers: []           // list of image files
    property int currentIndex: 0          // current wallpaper index
    property bool cycling: false          // cycling state

    Process { id: wallpaperProcess }

    FolderDialog {
        id: folderDialog
        title: "Select Wallpaper Folder"
        onAccepted: {
            wallpaperFolder = folderDialog.folder
            loadWallpapers()
        }
    }

    Timer {
        id: cycleTimer
        interval: 60000   // 1 minute, change as needed
        repeat: true
        running: cycling
        onTriggered: nextWallpaper()
    }

    ColumnLayout {
        anchors.centerIn: parent
        spacing: 12

        Button {
            text: "Select Wallpaper Folder"
            onClicked: folderDialog.open()
        }

        Button {
            text: cycling ? "Stop Cycling" : "Start Cycling"
            enabled: wallpapers.length > 0
            onClicked: {
                cycling = !cycling
                if (cycling) {
                    cycleTimer.start()
                } else {
                    cycleTimer.stop()
                }
            }
        }

        Button {
            text: "Next Wallpaper"
            enabled: wallpapers.length > 0
            onClicked: nextWallpaper()
        }
    }

    function loadWallpapers() {
        if (!wallpaperFolder) return

        var proc = Qt.createQmlObject('import Quickshell.Io; Process {}', root)
        proc.finished.connect(function() {
            wallpapers = proc.stdout.trim().split("\n")
            currentIndex = 0
            if (wallpapers.length > 0) {
                setWallpaper(wallpapers[0])
            }
        })
        proc.start("bash", ["-c", "find \"" + wallpaperFolder + "\" -type f \\( -iname '*.jpg' -o -iname '*.png' \\) | sort"])
    }

    function nextWallpaper() {
        if (wallpapers.length === 0) return
        currentIndex = (currentIndex + 1) % wallpapers.length
        setWallpaper(wallpapers[currentIndex])
    }

    function setWallpaper(file) {
        if (!file) return
        wallpaperProcess.start("/home/<USER>/.config/quickshell/ii/scripts/colors/switchwall.sh", [file])
    }
}
